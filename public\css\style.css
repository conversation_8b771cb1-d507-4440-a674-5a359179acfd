/* KickBot Dashboard Styles */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.navbar-brand {
    font-weight: bold;
}

.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.card-header {
    background-color: #fff;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
}

.chat-log {
    height: 400px;
    overflow-y: auto;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    padding: 15px;
    font-family: 'Courier New', monospace;
    font-size: 14px;
}

.chat-message {
    margin-bottom: 8px;
    padding: 5px 8px;
    border-radius: 4px;
    background-color: #fff;
    border-left: 3px solid #007bff;
}

.chat-message.bot {
    border-left-color: #28a745;
    background-color: #f8fff9;
}

.chat-message.error {
    border-left-color: #dc3545;
    background-color: #fff8f8;
}

.chat-timestamp {
    color: #6c757d;
    font-size: 12px;
}

.chat-username {
    font-weight: bold;
    color: #495057;
}

.chat-content {
    margin-left: 10px;
}

.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 5px;
}

.status-online {
    background-color: #28a745;
}

.status-offline {
    background-color: #dc3545;
}

.status-connecting {
    background-color: #ffc107;
    animation: pulse 1.5s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.btn {
    border-radius: 6px;
    font-weight: 500;
}

.form-control {
    border-radius: 6px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .chat-log {
        height: 300px;
    }
}

/* Loading spinner */
.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255,255,255,.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Toast notifications */
.toast-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
}

.toast {
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* Custom scrollbar for chat log */
.chat-log::-webkit-scrollbar {
    width: 8px;
}

.chat-log::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.chat-log::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.chat-log::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Giveaway styles */
.participant-list {
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.5rem;
    background-color: #f8f9fa;
}

.participant-item {
    display: inline-block;
    background: linear-gradient(45deg, #007bff, #0056b3);
    color: white;
    padding: 0.25rem 0.5rem;
    margin: 0.125rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.giveaway-timer {
    font-size: 1.2rem;
    font-weight: bold;
}

/* Chatters styles */
.chatter-item {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    transition: all 0.2s;
    background-color: white;
}

.chatter-item:hover {
    background-color: #f8f9fa;
    border-color: #adb5bd;
    transform: translateX(2px);
}

.chatter-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(45deg, #007bff, #6f42c1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 1.1rem;
}

.chatter-info {
    flex: 1;
    margin-left: 0.75rem;
}

.chatter-username {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.25rem;
}

.chatter-stats {
    font-size: 0.875rem;
    color: #6c757d;
}

.chatter-badges {
    display: flex;
    gap: 0.25rem;
}

.chatter-badge {
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
}

.badge-moderator {
    background-color: #28a745;
    color: white;
}

.badge-subscriber {
    background-color: #6f42c1;
    color: white;
}

.badge-vip {
    background-color: #ffc107;
    color: #212529;
}

/* Stream stats styles */
.stream-stat-card {
    transition: all 0.2s;
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.stream-stat-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

.stat-icon {
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
}

.stat-value {
    font-size: 2rem;
    font-weight: bold;
    margin: 0;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.5rem;
}

/* Chart container */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Active hours visualization */
.hour-bar {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: background-color 0.2s;
}

.hour-bar:hover {
    background-color: #f8f9fa;
}

.hour-label {
    width: 60px;
    font-size: 0.875rem;
    font-weight: 500;
}

.hour-progress {
    flex: 1;
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
    margin: 0 0.5rem;
}

.hour-progress-bar {
    height: 100%;
    background: linear-gradient(45deg, #007bff, #0056b3);
    transition: width 0.3s ease;
}

.hour-value {
    width: 40px;
    text-align: right;
    font-size: 0.875rem;
    color: #6c757d;
}
