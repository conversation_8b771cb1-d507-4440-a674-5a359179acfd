const axios = require('axios');
const crypto = require('crypto');
const config = require('../config/config');
const Logger = require('../utils/logger');

class KickAPI {
    constructor() {
        this.accessToken = null;
        this.refreshToken = null;
        this.tokenExpiry = null;
        this.codeVerifier = null;
        this.codeChallenge = null;
    }

    // Generování PKCE kódů pro OAuth
    generatePKCE() {
        this.codeVerifier = crypto.randomBytes(32).toString('base64url');
        this.codeChallenge = crypto
            .createHash('sha256')
            .update(this.codeVerifier)
            .digest('base64url');
        
        Logger.debug('PKCE kódy vygenerovány');
        return { codeVerifier: this.codeVerifier, codeChallenge: this.codeChallenge };
    }

    // Získání URL pro autorizaci
    getAuthorizationUrl(state) {
        if (!this.codeChallenge) {
            this.generatePKCE();
        }

        const params = new URLSearchParams({
            response_type: 'code',
            client_id: config.kick.clientId,
            redirect_uri: config.kick.redirectUri,
            scope: config.kick.scopes.join(' '),
            code_challenge: this.codeChallenge,
            code_challenge_method: 'S256',
            state: state
        });

        const authUrl = `${config.kick.oauthBaseUrl}/authorize?${params.toString()}`;
        Logger.info('Authorization URL vygenerována', { url: authUrl });
        return authUrl;
    }

    // Výměna authorization code za access token
    async exchangeCodeForToken(code) {
        try {
            const response = await axios.post(`${config.kick.oauthBaseUrl}/token`, {
                grant_type: 'authorization_code',
                client_id: config.kick.clientId,
                client_secret: config.kick.clientSecret,
                redirect_uri: config.kick.redirectUri,
                code: code,
                code_verifier: this.codeVerifier
            }, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });

            const { access_token, refresh_token, expires_in, scope } = response.data;
            
            this.accessToken = access_token;
            this.refreshToken = refresh_token;
            this.tokenExpiry = new Date(Date.now() + expires_in * 1000);

            Logger.success('Access token úspěšně získán', { 
                expires_in, 
                scope,
                expires_at: this.tokenExpiry.toISOString()
            });

            return {
                accessToken: this.accessToken,
                refreshToken: this.refreshToken,
                expiresIn: expires_in,
                scope: scope
            };
        } catch (error) {
            Logger.error('Chyba při výměně kódu za token', {
                error: error.response?.data || error.message,
                status: error.response?.status
            });
            throw new Error('Nepodařilo se získat access token');
        }
    }

    // Obnovení access tokenu
    async refreshAccessToken() {
        if (!this.refreshToken) {
            throw new Error('Refresh token není k dispozici');
        }

        try {
            const response = await axios.post(`${config.kick.oauthBaseUrl}/token`, {
                grant_type: 'refresh_token',
                client_id: config.kick.clientId,
                client_secret: config.kick.clientSecret,
                refresh_token: this.refreshToken
            }, {
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });

            const { access_token, refresh_token, expires_in } = response.data;
            
            this.accessToken = access_token;
            this.refreshToken = refresh_token;
            this.tokenExpiry = new Date(Date.now() + expires_in * 1000);

            Logger.success('Access token obnoven');
            return this.accessToken;
        } catch (error) {
            Logger.error('Chyba při obnovování tokenu', error.response?.data || error.message);
            throw new Error('Nepodařilo se obnovit access token');
        }
    }

    // Kontrola platnosti tokenu
    isTokenValid() {
        return this.accessToken && this.tokenExpiry && new Date() < this.tokenExpiry;
    }

    // Automatické obnovení tokenu pokud je potřeba
    async ensureValidToken() {
        if (!this.isTokenValid() && this.refreshToken) {
            await this.refreshAccessToken();
        }
        return this.accessToken;
    }

    // Získání informací o uživateli
    async getCurrentUser() {
        await this.ensureValidToken();

        try {
            const response = await axios.get(`${config.kick.apiBaseUrl}/users`, {
                headers: {
                    'Authorization': `Bearer ${this.accessToken}`,
                    'Accept': 'application/json'
                }
            });

            // API vrací pole uživatelů, bez ID parametru vrátí aktuálního uživatele
            const userData = response.data.data[0];
            Logger.info('Informace o uživateli získány', { name: userData.name, user_id: userData.user_id });
            return {
                id: userData.user_id,
                username: userData.name,
                name: userData.name,
                email: userData.email,
                profile_pic: userData.profile_picture
            };
        } catch (error) {
            Logger.error('Chyba při získávání informací o uživateli', error.response?.data || error.message);
            throw error;
        }
    }

    // Poslání zprávy do chatu
    async sendChatMessage(channelId, message, type = 'bot') {
        await this.ensureValidToken();

        try {
            const payload = {
                content: message,
                type: type
            };

            // Pokud posíláme jako user, potřebujeme broadcaster_user_id
            if (type === 'user') {
                payload.broadcaster_user_id = channelId;
            }

            const response = await axios.post(`${config.kick.apiBaseUrl}/chat`, payload, {
                headers: {
                    'Authorization': `Bearer ${this.accessToken}`,
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                }
            });

            Logger.info('Zpráva odeslána do chatu', {
                message,
                type,
                channelId,
                messageId: response.data?.data?.message_id
            });
            return response.data;
        } catch (error) {
            Logger.error('Chyba při odesílání zprávy', {
                error: error.response?.data || error.message,
                status: error.response?.status,
                message,
                channelId
            });
            throw error;
        }
    }

    // Získání informací o kanálu
    async getChannelInfo(channelSlug) {
        await this.ensureValidToken();

        try {
            // Zkusíme nejdřív oficiální API endpoint
            const response = await axios.get(`${config.kick.apiBaseUrl}/channels`, {
                params: {
                    slug: channelSlug
                },
                headers: {
                    'Authorization': `Bearer ${this.accessToken}`,
                    'Accept': 'application/json'
                }
            });

            Logger.info('Informace o kanálu získány přes API', {
                channel: channelSlug,
                data: response.data
            });

            // API vrací pole kanálů
            const channelData = response.data.data[0];
            if (!channelData) {
                throw new Error(`Kanál ${channelSlug} nebyl nalezen`);
            }

            // Vytvoříme mock chatroom ID pro testování
            return {
                id: channelData.broadcaster_user_id,
                slug: channelData.slug,
                chatroom: {
                    id: channelData.broadcaster_user_id // Použijeme broadcaster_user_id jako chatroom ID
                },
                livestream: channelData.stream
            };

        } catch (error) {
            Logger.error('Chyba při získávání informací o kanálu', {
                channel: channelSlug,
                error: error.response?.data || error.message,
                status: error.response?.status
            });
            throw error;
        }
    }

    // Zrušení tokenu
    async revokeToken() {
        if (!this.accessToken) return;

        try {
            await axios.post(`${config.kick.oauthBaseUrl}/revoke`, null, {
                params: {
                    token: this.accessToken,
                    token_hint_type: 'access_token'
                },
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded'
                }
            });

            this.accessToken = null;
            this.refreshToken = null;
            this.tokenExpiry = null;

            Logger.info('Token zrušen');
        } catch (error) {
            Logger.error('Chyba při rušení tokenu', error.response?.data || error.message);
        }
    }
}

module.exports = KickAPI;
