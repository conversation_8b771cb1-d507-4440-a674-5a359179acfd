const express = require('express');
const Logger = require('../utils/logger');
const AutoMessageService = require('../services/AutoMessageService');

const router = express.Router();

// Globální instance AutoMessageService
const autoMessageService = new AutoMessageService();

// Middleware pro kontrolu autentifikace
function requireAuth(req, res, next) {
    // Získáme KickAPI instanci z auth routeru
    const kickAPI = req.app.locals.kickAPI;

    if (!kickAPI || !kickAPI.isTokenValid()) {
        return res.status(401).json({ error: 'Nepřihlášen' });
    }

    req.kickAPI = kickAPI;
    req.bot = req.app.locals.botInstance;
    req.autoMessageService = autoMessageService;

    // Nastavit bot instanci do AutoMessageService
    if (req.bot) {
        autoMessageService.setBot(req.bot);
    }

    next();
}

// Získání statistik dashboardu
router.get('/stats', requireAuth, async (req, res) => {
    try {
        // Zde by byly skute<PERSON>n<PERSON> statistiky z databáze nebo cache
        const stats = {
            botStatus: 'online', // online, offline, connecting
            messagesToday: 42,
            activeCommands: 8,
            uptime: '02:34:15',
            lastActivity: new Date().toISOString()
        };
        
        res.json(stats);
    } catch (error) {
        Logger.error('Chyba při získávání statistik', error.message);
        res.status(500).json({ error: 'Chyba při načítání statistik' });
    }
});

// Získání nastavení bota
router.get('/settings', requireAuth, async (req, res) => {
    try {
        // Zde by byla skutečná data z databáze
        const settings = {
            channel: process.env.BOT_CHANNEL || '',
            commandPrefix: '!',
            autoRespond: true,
            moderationEnabled: true,
            welcomeMessage: 'Vítejte v chatu!',
            commands: [
                { name: 'hello', response: 'Ahoj!', enabled: true },
                { name: 'time', response: 'Aktuální čas je: {time}', enabled: true },
                { name: 'uptime', response: 'Bot běží: {uptime}', enabled: true }
            ]
        };
        
        res.json(settings);
    } catch (error) {
        Logger.error('Chyba při získávání nastavení', error.message);
        res.status(500).json({ error: 'Chyba při načítání nastavení' });
    }
});

// Uložení nastavení bota
router.post('/settings', requireAuth, async (req, res) => {
    try {
        const { channel, commandPrefix, autoRespond, moderationEnabled, welcomeMessage } = req.body;
        
        // Validace
        if (commandPrefix && commandPrefix.length !== 1) {
            return res.status(400).json({ error: 'Prefix musí být jeden znak' });
        }
        
        // Zde by se uložilo do databáze
        const updatedSettings = {
            channel: channel || '',
            commandPrefix: commandPrefix || '!',
            autoRespond: autoRespond !== undefined ? autoRespond : true,
            moderationEnabled: moderationEnabled !== undefined ? moderationEnabled : true,
            welcomeMessage: welcomeMessage || 'Vítejte v chatu!'
        };
        
        Logger.info('Nastavení uloženo', updatedSettings);
        res.json({ success: true, settings: updatedSettings });
        
    } catch (error) {
        Logger.error('Chyba při ukládání nastavení', error.message);
        res.status(500).json({ error: 'Chyba při ukládání nastavení' });
    }
});

// Získání chat logu
router.get('/chat-log', requireAuth, async (req, res) => {
    try {
        const limit = parseInt(req.query.limit) || 50;
        
        // Zde by byl skutečný chat log z databáze
        const chatLog = [
            {
                id: 1,
                timestamp: new Date().toISOString(),
                username: 'TestUser',
                message: 'Ahoj všichni!',
                type: 'user'
            },
            {
                id: 2,
                timestamp: new Date().toISOString(),
                username: 'SulekBOT',
                message: 'Vítejte v chatu!',
                type: 'bot'
            }
        ];
        
        res.json({ messages: chatLog.slice(-limit) });
        
    } catch (error) {
        Logger.error('Chyba při získávání chat logu', error.message);
        res.status(500).json({ error: 'Chyba při načítání chat logu' });
    }
});

// Poslání manuální zprávy
router.post('/send-message', requireAuth, async (req, res) => {
    try {
        const { message } = req.body;

        if (!message || message.trim().length === 0) {
            return res.status(400).json({ error: 'Zpráva nesmí být prázdná' });
        }

        if (message.length > 500) {
            return res.status(400).json({ error: 'Zpráva je příliš dlouhá (max 500 znaků)' });
        }

        if (!req.bot || !req.bot.isConnected) {
            return res.status(400).json({ error: 'Bot není připojen k žádnému kanálu. Nejprve spusťte bota.' });
        }

        // Poslání zprávy přes bota
        await req.bot.sendMessage(message.trim());

        Logger.info('Manuální zpráva odeslána', { message, channel: req.bot.currentChannel });
        res.json({
            success: true,
            message: 'Zpráva odeslána'
        });

    } catch (error) {
        Logger.error('Chyba při odesílání zprávy', error.message);
        res.status(500).json({ error: 'Chyba při odesílání zprávy' });
    }
});

// Ovládání bota (start/stop)
router.post('/bot/:action', requireAuth, async (req, res) => {
    try {
        const { action } = req.params;
        const { channel } = req.body;

        if (!['start', 'stop', 'restart'].includes(action)) {
            return res.status(400).json({ error: 'Neplatná akce' });
        }

        if (action === 'start' || action === 'restart') {
            if (!channel) {
                return res.status(400).json({ error: 'Musíte zadat název kanálu' });
            }

            // Spuštění/restart bota
            if (action === 'restart' && req.bot && req.bot.isConnected) {
                req.bot.disconnect();
            }

            if (!req.bot) {
                // Vytvoření nové instance bota s KickAPI
                const KickBot = require('../bot/KickBot');
                req.bot = new KickBot(req.kickAPI);
                req.app.locals.botInstance = req.bot;
            }

            await req.bot.connect(channel);
            Logger.info(`Bot ${action === 'start' ? 'spuštěn' : 'restartován'}`, { channel });

            res.json({
                success: true,
                message: `Bot ${action === 'start' ? 'spuštěn' : 'restartován'} na kanálu ${channel}`,
                status: 'online',
                channel: channel
            });

        } else if (action === 'stop') {
            if (req.bot && req.bot.isConnected) {
                req.bot.disconnect();
            }

            Logger.info('Bot zastaven');
            res.json({
                success: true,
                message: 'Bot zastaven',
                status: 'offline'
            });
        }

    } catch (error) {
        Logger.error(`Chyba při ${req.params.action} bota`, error.message);
        res.status(500).json({ error: `Chyba při ${req.params.action} bota: ${error.message}` });
    }
});

// Získání informací o kanálu
router.get('/channel/:slug', requireAuth, async (req, res) => {
    try {
        const { slug } = req.params;
        const channelInfo = await req.kickAPI.getChannelInfo(slug);
        
        res.json({
            id: channelInfo.id,
            slug: channelInfo.slug,
            user: channelInfo.user,
            livestream: channelInfo.livestream,
            followers_count: channelInfo.followers_count
        });
        
    } catch (error) {
        Logger.error('Chyba při získávání info o kanálu', error.message);
        res.status(404).json({ error: 'Kanál nenalezen' });
    }
});

// === AUTOMATICKÉ ZPRÁVY ===

// Získání všech automatických zpráv
router.get('/auto-messages', requireAuth, async (req, res) => {
    try {
        const autoMessages = req.autoMessageService.getAllAutoMessages();
        const stats = req.autoMessageService.getStats();

        res.json({
            success: true,
            autoMessages,
            stats
        });

    } catch (error) {
        Logger.error('Chyba při získávání automatických zpráv', error.message);
        res.status(500).json({ error: 'Chyba při získávání automatických zpráv' });
    }
});

// Přidání nové automatické zprávy
router.post('/auto-messages', requireAuth, async (req, res) => {
    try {
        const { name, content, interval, enabled } = req.body;

        if (!name || !content || !interval) {
            return res.status(400).json({ error: 'Chybí povinné údaje' });
        }

        if (interval < 1 || interval > 120) {
            return res.status(400).json({ error: 'Interval musí být 1-120 minut' });
        }

        const autoMessage = req.autoMessageService.addAutoMessage({
            name,
            content,
            interval,
            enabled: enabled !== false
        });

        res.json({
            success: true,
            autoMessage
        });

    } catch (error) {
        Logger.error('Chyba při přidávání automatické zprávy', error.message);
        res.status(500).json({ error: 'Chyba při přidávání automatické zprávy' });
    }
});

// Aktualizace automatické zprávy
router.put('/auto-messages/:id', requireAuth, async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = req.body;

        const autoMessage = req.autoMessageService.updateAutoMessage(id, updateData);

        res.json({
            success: true,
            autoMessage
        });

    } catch (error) {
        Logger.error('Chyba při aktualizaci automatické zprávy', error.message);
        res.status(500).json({ error: error.message });
    }
});

// Smazání automatické zprávy
router.delete('/auto-messages/:id', requireAuth, async (req, res) => {
    try {
        const { id } = req.params;

        req.autoMessageService.deleteAutoMessage(id);

        res.json({ success: true });

    } catch (error) {
        Logger.error('Chyba při mazání automatické zprávy', error.message);
        res.status(500).json({ error: error.message });
    }
});

// Povolení/zakázání automatických zpráv
router.post('/auto-messages/toggle', requireAuth, async (req, res) => {
    try {
        const { enabled } = req.body;

        req.autoMessageService.setEnabled(enabled);

        res.json({
            success: true,
            enabled
        });

    } catch (error) {
        Logger.error('Chyba při přepínání automatických zpráv', error.message);
        res.status(500).json({ error: 'Chyba při přepínání automatických zpráv' });
    }
});

// Manuální odeslání automatické zprávy
router.post('/auto-messages/:id/send', requireAuth, async (req, res) => {
    try {
        const { id } = req.params;

        await req.autoMessageService.sendManually(id);

        res.json({ success: true });

    } catch (error) {
        Logger.error('Chyba při manuálním odesílání zprávy', error.message);
        res.status(500).json({ error: error.message });
    }
});

module.exports = router;
