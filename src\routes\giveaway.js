const express = require('express');
const router = express.Router();
const Logger = require('../utils/Logger');

// In-memory storage pro giveaways (v produkci by bylo v databázi)
let activeGiveaways = new Map();
let giveawayHistory = [];

// Middleware pro kontrolu autentifikace
function requireAuth(req, res, next) {
    const kickAPI = req.app.locals.kickAPI;
    
    if (!kickAPI || !kickAPI.isTokenValid()) {
        return res.status(401).json({ error: 'Nepřihlášen' });
    }
    
    req.kickAPI = kickAPI;
    next();
}

// Vytvoření nového giveaway
router.post('/create', requireAuth, async (req, res) => {
    try {
        const { title, description, duration, winners } = req.body;
        
        if (!title || !duration || !winners) {
            return res.status(400).json({ error: 'Chyb<PERSON> povinn<PERSON> ú<PERSON>' });
        }
        
        if (duration < 1 || duration > 60) {
            return res.status(400).json({ error: 'Doba trvání musí být 1-60 minut' });
        }
        
        if (winners < 1 || winners > 10) {
            return res.status(400).json({ error: 'Počet výherců musí být 1-10' });
        }
        
        // Kontrola, jestli už neběží jiný giveaway
        const existingGiveaway = Array.from(activeGiveaways.values())
            .find(g => g.status === 'active');
            
        if (existingGiveaway) {
            return res.status(400).json({ error: 'Již běží jiný giveaway' });
        }
        
        const giveaway = {
            id: Date.now().toString(),
            title: title.trim(),
            description: description?.trim() || '',
            duration: parseInt(duration),
            winners: parseInt(winners),
            status: 'active',
            startTime: new Date(),
            endTime: new Date(Date.now() + duration * 60000),
            participants: [],
            createdBy: req.kickAPI.currentUser?.username || 'Unknown'
        };
        
        activeGiveaways.set(giveaway.id, giveaway);
        
        Logger.info('Giveaway vytvořen', {
            id: giveaway.id,
            title: giveaway.title,
            duration: giveaway.duration,
            winners: giveaway.winners
        });
        
        // Automatické ukončení po vypršení času
        setTimeout(() => {
            const g = activeGiveaways.get(giveaway.id);
            if (g && g.status === 'active') {
                g.status = 'expired';
                Logger.info('Giveaway vypršel', { id: giveaway.id });
            }
        }, duration * 60000);
        
        res.json({
            success: true,
            giveaway: {
                id: giveaway.id,
                title: giveaway.title,
                description: giveaway.description,
                duration: giveaway.duration,
                winners: giveaway.winners,
                startTime: giveaway.startTime,
                endTime: giveaway.endTime
            }
        });
        
    } catch (error) {
        Logger.error('Chyba při vytváření giveaway', error.message);
        res.status(500).json({ error: 'Chyba při vytváření giveaway' });
    }
});

// Přidání účastníka do giveaway
router.post('/join', requireAuth, async (req, res) => {
    try {
        const { giveawayId, username } = req.body;
        
        if (!giveawayId || !username) {
            return res.status(400).json({ error: 'Chybí giveaway ID nebo username' });
        }
        
        const giveaway = activeGiveaways.get(giveawayId);
        
        if (!giveaway) {
            return res.status(404).json({ error: 'Giveaway nenalezen' });
        }
        
        if (giveaway.status !== 'active') {
            return res.status(400).json({ error: 'Giveaway není aktivní' });
        }
        
        if (new Date() > giveaway.endTime) {
            giveaway.status = 'expired';
            return res.status(400).json({ error: 'Giveaway již skončil' });
        }
        
        // Kontrola, jestli už není účastník přihlášen
        if (giveaway.participants.includes(username)) {
            return res.status(400).json({ error: 'Již jste přihlášen' });
        }
        
        giveaway.participants.push(username);
        
        Logger.info('Účastník přidán do giveaway', {
            giveawayId,
            username,
            totalParticipants: giveaway.participants.length
        });
        
        res.json({
            success: true,
            participantCount: giveaway.participants.length
        });
        
    } catch (error) {
        Logger.error('Chyba při přidávání účastníka', error.message);
        res.status(500).json({ error: 'Chyba při přidávání účastníka' });
    }
});

// Losování výherců
router.post('/draw', requireAuth, async (req, res) => {
    try {
        const { giveawayId, participants, winnerCount } = req.body;
        
        if (!giveawayId || !participants || !Array.isArray(participants)) {
            return res.status(400).json({ error: 'Neplatné údaje' });
        }
        
        const giveaway = activeGiveaways.get(giveawayId);
        
        if (!giveaway) {
            return res.status(404).json({ error: 'Giveaway nenalezen' });
        }
        
        if (participants.length === 0) {
            return res.status(400).json({ error: 'Žádní účastníci' });
        }
        
        const actualWinnerCount = Math.min(winnerCount || giveaway.winners, participants.length);
        const winners = [];
        const participantsCopy = [...participants];
        
        // Náhodné losování
        for (let i = 0; i < actualWinnerCount; i++) {
            const randomIndex = Math.floor(Math.random() * participantsCopy.length);
            winners.push(participantsCopy.splice(randomIndex, 1)[0]);
        }
        
        // Aktualizace giveaway
        giveaway.status = 'completed';
        giveaway.winners = winners;
        giveaway.completedAt = new Date();
        
        // Přesun do historie
        giveawayHistory.push({
            ...giveaway,
            finalParticipantCount: participants.length
        });
        
        Logger.info('Giveaway dokončen', {
            giveawayId,
            winners,
            participantCount: participants.length
        });
        
        res.json({
            success: true,
            winners,
            participantCount: participants.length
        });
        
    } catch (error) {
        Logger.error('Chyba při losování', error.message);
        res.status(500).json({ error: 'Chyba při losování' });
    }
});

// Ukončení giveaway
router.post('/end', requireAuth, async (req, res) => {
    try {
        const { giveawayId } = req.body;
        
        if (!giveawayId) {
            return res.status(400).json({ error: 'Chybí giveaway ID' });
        }
        
        const giveaway = activeGiveaways.get(giveawayId);
        
        if (!giveaway) {
            return res.status(404).json({ error: 'Giveaway nenalezen' });
        }
        
        giveaway.status = 'cancelled';
        giveaway.endedAt = new Date();
        
        // Přesun do historie
        giveawayHistory.push({
            ...giveaway,
            finalParticipantCount: giveaway.participants.length
        });
        
        activeGiveaways.delete(giveawayId);
        
        Logger.info('Giveaway ukončen', { giveawayId });
        
        res.json({ success: true });
        
    } catch (error) {
        Logger.error('Chyba při ukončování giveaway', error.message);
        res.status(500).json({ error: 'Chyba při ukončování giveaway' });
    }
});

// Získání aktivního giveaway
router.get('/active', requireAuth, async (req, res) => {
    try {
        const activeGiveaway = Array.from(activeGiveaways.values())
            .find(g => g.status === 'active');
            
        if (!activeGiveaway) {
            return res.json({ success: true, giveaway: null });
        }
        
        res.json({
            success: true,
            giveaway: {
                id: activeGiveaway.id,
                title: activeGiveaway.title,
                description: activeGiveaway.description,
                duration: activeGiveaway.duration,
                winners: activeGiveaway.winners,
                startTime: activeGiveaway.startTime,
                endTime: activeGiveaway.endTime,
                participantCount: activeGiveaway.participants.length,
                status: activeGiveaway.status
            }
        });
        
    } catch (error) {
        Logger.error('Chyba při získávání aktivního giveaway', error.message);
        res.status(500).json({ error: 'Chyba při získávání giveaway' });
    }
});

// Historie giveaways
router.get('/history', requireAuth, async (req, res) => {
    try {
        const history = giveawayHistory
            .slice(-10) // Posledních 10
            .reverse()
            .map(g => ({
                id: g.id,
                title: g.title,
                status: g.status,
                startTime: g.startTime,
                completedAt: g.completedAt || g.endedAt,
                participantCount: g.finalParticipantCount || g.participants.length,
                winners: g.winners || []
            }));
            
        res.json({ success: true, history });
        
    } catch (error) {
        Logger.error('Chyba při získávání historie', error.message);
        res.status(500).json({ error: 'Chyba při získávání historie' });
    }
});

module.exports = router;
