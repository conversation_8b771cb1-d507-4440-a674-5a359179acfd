// KickBot Dashboard JavaScript

class Dashboard {
    constructor() {
        this.isAuthenticated = false;
        this.botStatus = 'offline';
        this.currentChannel = null;
        this.updateInterval = null;
        this.activeGiveaway = null;
        this.giveawayTimer = null;
        this.participants = new Set();
        this.init();
    }

    async init() {
        this.setupEventListeners();
        await this.checkAuthStatus();
        this.handleUrlParams();
        
        if (this.isAuthenticated) {
            this.showAuthenticatedUI();
            this.startPeriodicUpdates();
        }
    }

    setupEventListeners() {
        // Auth buttons
        document.getElementById('login-btn')?.addEventListener('click', () => {
            window.location.href = '/auth/login';
        });

        // Bot control buttons
        document.getElementById('start-bot-btn')?.addEventListener('click', () => {
            this.startBot();
        });

        document.getElementById('stop-bot-btn')?.addEventListener('click', () => {
            this.stopBot();
        });

        // Settings
        document.getElementById('save-settings-btn')?.addEventListener('click', () => {
            this.saveSettings();
        });

        // Manual message
        document.getElementById('send-message-btn')?.addEventListener('click', () => {
            this.sendManualMessage();
        });

        // Enter key for manual message
        document.getElementById('manual-message')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && e.ctrlKey) {
                this.sendManualMessage();
            }
        });
    }

    async checkAuthStatus() {
        try {
            const response = await fetch('/auth/status');
            const data = await response.json();
            
            this.isAuthenticated = data.authenticated;
            
            if (this.isAuthenticated) {
                this.updateAuthStatus(data.user);
            } else {
                this.updateAuthStatus(null);
            }
        } catch (error) {
            console.error('Chyba při kontrole auth stavu:', error);
            this.showToast('Chyba při ověřování', 'error');
        }
    }

    updateAuthStatus(user) {
        const authStatus = document.getElementById('auth-status');
        
        if (user) {
            authStatus.innerHTML = `
                <span class="nav-link text-success">
                    <i class="fas fa-check-circle me-1"></i>
                    ${user.username}
                </span>
            `;
        } else {
            authStatus.innerHTML = `
                <span class="nav-link text-warning">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Nepřihlášen
                </span>
            `;
        }
    }

    showAuthenticatedUI() {
        document.getElementById('auth-section').style.display = 'none';
        document.getElementById('bot-controls').style.display = 'block';
        document.getElementById('main-content').style.display = 'block';
        
        this.loadDashboardData();
    }

    async loadDashboardData() {
        try {
            // Načtení statistik
            const statsResponse = await fetch('/dashboard/stats');
            if (statsResponse.ok) {
                const stats = await statsResponse.json();
                this.updateStats(stats);
            }

            // Načtení nastavení
            const settingsResponse = await fetch('/dashboard/settings');
            if (settingsResponse.ok) {
                const settings = await settingsResponse.json();
                this.updateSettings(settings);
            }

            // Načtení chat logu
            const chatResponse = await fetch('/dashboard/chat-log');
            if (chatResponse.ok) {
                const chatData = await chatResponse.json();
                this.updateChatLog(chatData.messages);
            }

        } catch (error) {
            console.error('Chyba při načítání dat:', error);
            this.showToast('Chyba při načítání dat dashboardu', 'error');
        }
    }

    updateStats(stats) {
        document.getElementById('bot-status').textContent = 
            stats.botStatus === 'online' ? 'Online' : 'Offline';
        document.getElementById('messages-today').textContent = stats.messagesToday;
        document.getElementById('active-commands').textContent = stats.activeCommands;
        document.getElementById('uptime').textContent = stats.uptime;

        // Update bot control buttons
        const startBtn = document.getElementById('start-bot-btn');
        const stopBtn = document.getElementById('stop-bot-btn');
        
        if (stats.botStatus === 'online') {
            startBtn.disabled = true;
            stopBtn.disabled = false;
            this.botStatus = 'online';
        } else {
            startBtn.disabled = false;
            stopBtn.disabled = true;
            this.botStatus = 'offline';
        }
    }

    updateSettings(settings) {
        document.getElementById('channel-input').value = settings.channel || '';
        document.getElementById('prefix-input').value = settings.commandPrefix || '!';
    }

    updateChatLog(messages) {
        const chatLog = document.getElementById('chat-log');
        
        if (!messages || messages.length === 0) {
            chatLog.innerHTML = '<p class="text-muted">Žádné zprávy v chatu...</p>';
            return;
        }

        const messagesHtml = messages.map(msg => `
            <div class="chat-message ${msg.type}">
                <span class="chat-timestamp">${new Date(msg.timestamp).toLocaleTimeString()}</span>
                <span class="chat-username">${msg.username}:</span>
                <span class="chat-content">${this.escapeHtml(msg.message)}</span>
            </div>
        `).join('');

        chatLog.innerHTML = messagesHtml;
        chatLog.scrollTop = chatLog.scrollHeight;
    }

    async connectToChannel() {
        const channel = document.getElementById('channel-input').value.trim();

        if (!channel) {
            this.showToast('Musíte zadat název kanálu', 'error');
            return;
        }

        // Validace názvu kanálu
        if (channel.includes('kick.com/')) {
            this.showToast('Zadejte pouze název kanálu bez "kick.com/"', 'error');
            return;
        }

        this.showToast(`Připojování k kanálu ${channel}...`, 'info');
        await this.startBot();
    }

    async startBot() {
        const channel = document.getElementById('channel-input').value.trim();

        if (!channel) {
            this.showToast('Musíte zadat název kanálu', 'error');
            return;
        }

        try {
            const response = await fetch('/dashboard/bot/start', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ channel })
            });

            const data = await response.json();

            if (data.success) {
                this.showToast(data.message, 'success');
                this.botStatus = 'online';
                this.currentChannel = channel;
                this.updateBotControls();
            } else {
                this.showToast(data.error || 'Chyba při spouštění bota', 'error');
            }
        } catch (error) {
            console.error('Chyba při spouštění bota:', error);
            this.showToast('Chyba při spouštění bota', 'error');
        }
    }

    async stopBot() {
        try {
            const response = await fetch('/dashboard/bot/stop', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            const data = await response.json();
            
            if (data.success) {
                this.showToast(data.message, 'success');
                this.botStatus = 'offline';
                this.currentChannel = null;
                this.updateBotControls();
            } else {
                this.showToast(data.error || 'Chyba při zastavování bota', 'error');
            }
        } catch (error) {
            console.error('Chyba při zastavování bota:', error);
            this.showToast('Chyba při zastavování bota', 'error');
        }
    }

    updateBotControls() {
        const startBtn = document.getElementById('start-bot-btn');
        const stopBtn = document.getElementById('stop-bot-btn');
        const statusIndicator = document.getElementById('bot-status-indicator');
        const connectedChannel = document.getElementById('connected-channel');

        if (this.botStatus === 'online') {
            startBtn.disabled = true;
            stopBtn.disabled = false;
            statusIndicator.innerHTML = '<i class="fas fa-circle me-1"></i>Online';
            statusIndicator.className = 'fw-bold text-success';
            connectedChannel.textContent = this.currentChannel || '-';
            connectedChannel.className = 'fw-bold text-success';
        } else {
            startBtn.disabled = false;
            stopBtn.disabled = true;
            statusIndicator.innerHTML = '<i class="fas fa-circle me-1"></i>Offline';
            statusIndicator.className = 'fw-bold text-danger';
            connectedChannel.textContent = '-';
            connectedChannel.className = 'fw-bold text-muted';
        }
    }

    async saveSettings() {
        const channel = document.getElementById('channel-input').value.trim();
        const prefix = document.getElementById('prefix-input').value.trim();

        try {
            const response = await fetch('/dashboard/settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    channel,
                    commandPrefix: prefix
                })
            });

            const data = await response.json();
            
            if (data.success) {
                this.showToast('Nastavení uloženo', 'success');
            } else {
                this.showToast(data.error || 'Chyba při ukládání nastavení', 'error');
            }
        } catch (error) {
            console.error('Chyba při ukládání nastavení:', error);
            this.showToast('Chyba při ukládání nastavení', 'error');
        }
    }

    async sendManualMessage() {
        const messageInput = document.getElementById('manual-message');
        const message = messageInput.value.trim();

        if (!message) {
            this.showToast('Zpráva nesmí být prázdná', 'error');
            return;
        }

        try {
            const response = await fetch('/dashboard/send-message', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ message })
            });

            const data = await response.json();
            
            if (data.success) {
                this.showToast('Zpráva odeslána', 'success');
                messageInput.value = '';
            } else {
                this.showToast(data.error || 'Chyba při odesílání zprávy', 'error');
            }
        } catch (error) {
            console.error('Chyba při odesílání zprávy:', error);
            this.showToast('Chyba při odesílání zprávy', 'error');
        }
    }

    startPeriodicUpdates() {
        this.updateInterval = setInterval(() => {
            if (this.isAuthenticated) {
                this.loadDashboardData();
            }
        }, 30000); // Každých 30 sekund
    }

    handleUrlParams() {
        const urlParams = new URLSearchParams(window.location.search);
        
        if (urlParams.get('auth') === 'success') {
            this.showToast('Úspěšně přihlášen!', 'success');
            // Vyčistit URL
            window.history.replaceState({}, document.title, window.location.pathname);
        }
        
        if (urlParams.get('error')) {
            const error = urlParams.get('error');
            let message = 'Chyba při přihlašování';
            
            switch (error) {
                case 'oauth_error':
                    message = 'Chyba OAuth autentifikace';
                    break;
                case 'invalid_state':
                    message = 'Neplatný stav autentifikace';
                    break;
                case 'auth_failed':
                    message = 'Přihlášení se nezdařilo';
                    break;
            }
            
            this.showToast(message, 'error');
            window.history.replaceState({}, document.title, window.location.pathname);
        }
    }

    showToast(message, type = 'info') {
        // Vytvoření toast notifikace
        const toastHtml = `
            <div class="toast align-items-center text-white bg-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'primary'} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="fas fa-${type === 'error' ? 'exclamation-circle' : type === 'success' ? 'check-circle' : 'info-circle'} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;

        // Přidání do kontejneru
        let container = document.querySelector('.toast-container');
        if (!container) {
            container = document.createElement('div');
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            document.body.appendChild(container);
        }

        container.insertAdjacentHTML('beforeend', toastHtml);
        
        // Zobrazení toast
        const toastElement = container.lastElementChild;
        const toast = new bootstrap.Toast(toastElement);
        toast.show();

        // Automatické odstranění po skrytí
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // === COMMANDS MANAGEMENT ===
    async loadCommands() {
        try {
            const response = await fetch('/api/bot/commands');
            const data = await response.json();

            const commandsList = document.getElementById('commands-list');
            if (data.commands && data.commands.length > 0) {
                commandsList.innerHTML = data.commands.map(cmd => `
                    <div class="card mb-2">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="card-title">${this.escapeHtml(cmd.name || cmd.trigger)}</h6>
                                    <p class="card-text">
                                        <strong>Trigger:</strong> ${this.escapeHtml(cmd.trigger)}<br>
                                        <strong>Odpověď:</strong> ${this.escapeHtml(cmd.response)}<br>
                                        <small class="text-muted">Cooldown: ${cmd.cooldown}s</small>
                                    </p>
                                </div>
                                <button class="btn btn-danger btn-sm" onclick="dashboard.deleteCommand('${cmd.trigger}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('');
            } else {
                commandsList.innerHTML = '<p class="text-muted">Žádné příkazy nenalezeny</p>';
            }
        } catch (error) {
            console.error('Chyba při načítání příkazů:', error);
            document.getElementById('commands-list').innerHTML = '<p class="text-danger">Chyba při načítání příkazů</p>';
        }
    }

    async saveCommand() {
        const name = document.getElementById('commandName').value.trim();
        const trigger = document.getElementById('commandTrigger').value.trim();
        const response = document.getElementById('commandResponse').value.trim();
        const cooldown = parseInt(document.getElementById('commandCooldown').value) || 5;

        if (!name || !trigger || !response) {
            this.showToast('Všechna pole jsou povinná', 'error');
            return;
        }

        try {
            const apiResponse = await fetch('/api/bot/commands', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ name, trigger, response, cooldown })
            });

            const data = await apiResponse.json();

            if (data.success) {
                this.showToast('Příkaz byl přidán', 'success');
                bootstrap.Modal.getInstance(document.getElementById('addCommandModal')).hide();
                document.getElementById('addCommandForm').reset();
                this.loadCommands();
            } else {
                this.showToast(data.error || 'Chyba při přidávání příkazu', 'error');
            }
        } catch (error) {
            console.error('Chyba při ukládání příkazu:', error);
            this.showToast('Chyba při ukládání příkazu', 'error');
        }
    }

    async deleteCommand(trigger) {
        if (!confirm('Opravdu chcete smazat tento příkaz?')) {
            return;
        }

        try {
            const response = await fetch(`/api/bot/commands/${encodeURIComponent(trigger)}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.success) {
                this.showToast('Příkaz byl smazán', 'success');
                this.loadCommands();
            } else {
                this.showToast(data.error || 'Chyba při mazání příkazu', 'error');
            }
        } catch (error) {
            console.error('Chyba při mazání příkazu:', error);
            this.showToast('Chyba při mazání příkazu', 'error');
        }
    }

    // === AUTO-RESPONSES MANAGEMENT ===
    async loadAutoResponses() {
        try {
            const response = await fetch('/api/bot/auto-responses');
            const data = await response.json();

            const autoResponsesList = document.getElementById('auto-responses-list');
            if (data.rules && data.rules.length > 0) {
                autoResponsesList.innerHTML = data.rules.map(rule => `
                    <div class="card mb-2">
                        <div class="card-body">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="card-title">
                                        ${this.escapeHtml(rule.name)}
                                        <span class="badge bg-${rule.enabled ? 'success' : 'secondary'} ms-2">
                                            ${rule.enabled ? 'Zapnuto' : 'Vypnuto'}
                                        </span>
                                    </h6>
                                    <p class="card-text">
                                        <strong>Triggery:</strong> ${rule.triggers.map(t => this.escapeHtml(t)).join(', ')}<br>
                                        <strong>Odpověď:</strong> ${this.escapeHtml(rule.response)}<br>
                                        <small class="text-muted">Cooldown: ${rule.cooldown}s | Použito: ${rule.triggerCount}x</small>
                                    </p>
                                </div>
                                <div class="btn-group-vertical btn-group-sm">
                                    <button class="btn btn-outline-${rule.enabled ? 'warning' : 'success'}"
                                            onclick="dashboard.toggleAutoResponse('${rule.id}', ${!rule.enabled})">
                                        <i class="fas fa-${rule.enabled ? 'pause' : 'play'}"></i>
                                    </button>
                                    <button class="btn btn-outline-danger" onclick="dashboard.deleteAutoResponse('${rule.id}')">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('');
            } else {
                autoResponsesList.innerHTML = '<p class="text-muted">Žádné automatické odpovědi nenalezeny</p>';
            }
        } catch (error) {
            console.error('Chyba při načítání auto-responses:', error);
            document.getElementById('auto-responses-list').innerHTML = '<p class="text-danger">Chyba při načítání automatických odpovědí</p>';
        }
    }

    async saveAutoResponse() {
        const name = document.getElementById('autoResponseName').value.trim();
        const triggers = document.getElementById('autoResponseTriggers').value.trim();
        const response = document.getElementById('autoResponseResponse').value.trim();
        const cooldown = parseInt(document.getElementById('autoResponseCooldown').value) || 30;

        if (!name || !triggers || !response) {
            this.showToast('Všechna pole jsou povinná', 'error');
            return;
        }

        try {
            const apiResponse = await fetch('/api/bot/auto-responses', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ name, triggers, response, cooldown })
            });

            const data = await apiResponse.json();

            if (data.success) {
                this.showToast('Automatická odpověď byla přidána', 'success');
                bootstrap.Modal.getInstance(document.getElementById('addAutoResponseModal')).hide();
                document.getElementById('addAutoResponseForm').reset();
                this.loadAutoResponses();
            } else {
                this.showToast(data.error || 'Chyba při přidávání automatické odpovědi', 'error');
            }
        } catch (error) {
            console.error('Chyba při ukládání auto-response:', error);
            this.showToast('Chyba při ukládání automatické odpovědi', 'error');
        }
    }

    async toggleAutoResponse(ruleId, enabled) {
        try {
            const response = await fetch(`/api/bot/auto-responses/${ruleId}/toggle`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ enabled })
            });

            const data = await response.json();

            if (data.success) {
                this.showToast(`Automatická odpověď ${enabled ? 'zapnuta' : 'vypnuta'}`, 'success');
                this.loadAutoResponses();
            } else {
                this.showToast(data.error || 'Chyba při přepínání automatické odpovědi', 'error');
            }
        } catch (error) {
            console.error('Chyba při přepínání auto-response:', error);
            this.showToast('Chyba při přepínání automatické odpovědi', 'error');
        }
    }

    async deleteAutoResponse(ruleId) {
        if (!confirm('Opravdu chcete smazat tuto automatickou odpověď?')) {
            return;
        }

        try {
            const response = await fetch(`/api/bot/auto-responses/${ruleId}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.success) {
                this.showToast('Automatická odpověď byla smazána', 'success');
                this.loadAutoResponses();
            } else {
                this.showToast(data.error || 'Chyba při mazání automatické odpovědi', 'error');
            }
        } catch (error) {
            console.error('Chyba při mazání auto-response:', error);
            this.showToast('Chyba při mazání automatické odpovědi', 'error');
        }
    }

    // === STATISTICS ===
    async loadStatistics() {
        try {
            const response = await fetch('/api/bot/stats/detailed');
            const data = await response.json();

            this.displayStatistics(data);
        } catch (error) {
            console.error('Chyba při načítání statistik:', error);
            document.getElementById('stats-overview').innerHTML = '<p class="text-danger">Chyba při načítání statistik</p>';
        }
    }

    displayStatistics(data) {
        const overview = data.overview || {};
        const topChatters = data.topChatters || [];

        // Přehled statistik
        document.getElementById('stats-overview').innerHTML = `
            <div class="row">
                <div class="col-6 mb-3">
                    <div class="text-center">
                        <h4 class="text-primary">${overview.uptime || '0s'}</h4>
                        <small class="text-muted">Uptime</small>
                    </div>
                </div>
                <div class="col-6 mb-3">
                    <div class="text-center">
                        <h4 class="text-success">${overview.messagesReceived || 0}</h4>
                        <small class="text-muted">Zpráv přijato</small>
                    </div>
                </div>
                <div class="col-6 mb-3">
                    <div class="text-center">
                        <h4 class="text-info">${overview.commandsExecuted || 0}</h4>
                        <small class="text-muted">Příkazů vykonáno</small>
                    </div>
                </div>
                <div class="col-6 mb-3">
                    <div class="text-center">
                        <h4 class="text-warning">${overview.uniqueUsers || 0}</h4>
                        <small class="text-muted">Unikátních uživatelů</small>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-12">
                    <small class="text-muted">
                        Auto-odpovědi: ${overview.autoResponsesTriggered || 0} |
                        Chyby: ${overview.errors || 0} |
                        Průměrný čas odpovědi: ${overview.averageResponseTime || 0}ms
                    </small>
                </div>
            </div>
        `;

        // Top chatteři
        if (topChatters.length > 0) {
            document.getElementById('top-chatters').innerHTML = topChatters.map((chatter, index) => `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <div>
                        <span class="badge bg-primary me-2">${index + 1}</span>
                        <strong>${this.escapeHtml(chatter.username)}</strong>
                    </div>
                    <span class="text-muted">${chatter.messageCount} zpráv</span>
                </div>
            `).join('');
        } else {
            document.getElementById('top-chatters').innerHTML = '<p class="text-muted">Žádní chatteři zatím</p>';
        }
    }

    // === GIVEAWAY FUNKCE ===

    async createGiveaway() {
        const title = document.getElementById('giveawayTitle').value.trim();
        const description = document.getElementById('giveawayDescription').value.trim();
        const duration = parseInt(document.getElementById('giveawayDuration').value);
        const winners = parseInt(document.getElementById('giveawayWinners').value);

        if (!title) {
            this.showToast('Musíte zadat název giveaway', 'error');
            return;
        }

        if (this.activeGiveaway) {
            this.showToast('Již běží jiný giveaway', 'error');
            return;
        }

        try {
            const response = await fetch('/dashboard/giveaway/create', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ title, description, duration, winners })
            });

            const data = await response.json();

            if (data.success) {
                this.activeGiveaway = {
                    id: data.giveaway.id,
                    title,
                    description,
                    duration,
                    winners,
                    startTime: new Date(),
                    endTime: new Date(Date.now() + duration * 60000)
                };

                this.participants.clear();
                this.startGiveawayTimer();
                this.updateGiveawayDisplay();
                this.showToast('Giveaway spuštěn!', 'success');

                // Vyčistit formulář
                document.getElementById('createGiveawayForm').reset();
            } else {
                this.showToast(data.error || 'Chyba při vytváření giveaway', 'error');
            }
        } catch (error) {
            console.error('Chyba při vytváření giveaway:', error);
            this.showToast('Chyba při vytváření giveaway', 'error');
        }
    }

    startGiveawayTimer() {
        if (this.giveawayTimer) {
            clearInterval(this.giveawayTimer);
        }

        this.giveawayTimer = setInterval(() => {
            if (!this.activeGiveaway) {
                clearInterval(this.giveawayTimer);
                return;
            }

            const now = new Date();
            const timeLeft = this.activeGiveaway.endTime - now;

            if (timeLeft <= 0) {
                this.endGiveaway();
                return;
            }

            const minutes = Math.floor(timeLeft / 60000);
            const seconds = Math.floor((timeLeft % 60000) / 1000);

            document.getElementById('giveaway-timer').textContent =
                `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }, 1000);
    }

    updateGiveawayDisplay() {
        const activeGiveawayDiv = document.getElementById('active-giveaway');
        const participantsDiv = document.getElementById('giveaway-participants');

        if (this.activeGiveaway) {
            activeGiveawayDiv.innerHTML = `
                <h6>${this.escapeHtml(this.activeGiveaway.title)}</h6>
                <p class="text-muted mb-2">${this.escapeHtml(this.activeGiveaway.description || 'Bez popisu')}</p>
                <small class="text-info">
                    <i class="fas fa-trophy me-1"></i>
                    ${this.activeGiveaway.winners} ${this.activeGiveaway.winners === 1 ? 'výherce' : 'výherci'}
                </small>
            `;

            participantsDiv.style.display = 'block';
            this.updateParticipantsList();
        } else {
            activeGiveawayDiv.innerHTML = '<p class="text-muted">Žádný aktivní giveaway</p>';
            participantsDiv.style.display = 'none';
            document.getElementById('giveaway-timer').textContent = '--:--';
        }
    }

    updateParticipantsList() {
        const participantsList = document.getElementById('participant-list');
        const participantCount = document.getElementById('participant-count');

        participantCount.textContent = this.participants.size;

        if (this.participants.size > 0) {
            participantsList.innerHTML = Array.from(this.participants)
                .map(username => `<span class="participant-item">${this.escapeHtml(username)}</span>`)
                .join('');
        } else {
            participantsList.innerHTML = '<p class="text-muted small">Zatím žádní účastníci</p>';
        }
    }

    async drawWinner() {
        if (!this.activeGiveaway) {
            this.showToast('Žádný aktivní giveaway', 'error');
            return;
        }

        if (this.participants.size === 0) {
            this.showToast('Žádní účastníci v giveaway', 'error');
            return;
        }

        try {
            const response = await fetch('/dashboard/giveaway/draw', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    giveawayId: this.activeGiveaway.id,
                    participants: Array.from(this.participants),
                    winnerCount: this.activeGiveaway.winners
                })
            });

            const data = await response.json();

            if (data.success) {
                const winners = data.winners;
                const winnerText = winners.length === 1 ?
                    `Výherce: ${winners[0]}` :
                    `Výherci: ${winners.join(', ')}`;

                this.showToast(`🎉 ${winnerText}`, 'success');

                // Oznámit výherce do chatu
                if (this.botStatus === 'online') {
                    const message = `🎉 Giveaway "${this.activeGiveaway.title}" skončil! ${winnerText}`;
                    await this.sendMessage(message);
                }

                this.endGiveaway();
            } else {
                this.showToast(data.error || 'Chyba při losování', 'error');
            }
        } catch (error) {
            console.error('Chyba při losování:', error);
            this.showToast('Chyba při losování', 'error');
        }
    }

    async endGiveaway() {
        if (!this.activeGiveaway) return;

        try {
            await fetch('/dashboard/giveaway/end', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ giveawayId: this.activeGiveaway.id })
            });
        } catch (error) {
            console.error('Chyba při ukončování giveaway:', error);
        }

        this.activeGiveaway = null;
        this.participants.clear();

        if (this.giveawayTimer) {
            clearInterval(this.giveawayTimer);
            this.giveawayTimer = null;
        }

        this.updateGiveawayDisplay();
        this.showToast('Giveaway ukončen', 'info');
    }

    // Simulace přidání účastníka (později bude z WebSocket chatu)
    addGiveawayParticipant(username) {
        if (this.activeGiveaway && username) {
            this.participants.add(username);
            this.updateParticipantsList();
        }
    }

    // === CHATTERS FUNKCE ===

    async refreshChatters() {
        try {
            const response = await fetch('/dashboard/chatters');
            const data = await response.json();

            if (data.success) {
                this.displayChatters(data.chatters);
            } else {
                this.showToast('Chyba při načítání chatterů', 'error');
            }
        } catch (error) {
            console.error('Chyba při načítání chatterů:', error);
            this.showToast('Chyba při načítání chatterů', 'error');
        }
    }

    displayChatters(chatters) {
        const chattersList = document.getElementById('chatters-list');

        if (chatters.length === 0) {
            chattersList.innerHTML = '<p class="text-muted">Žádní aktivní chatteři</p>';
            return;
        }

        chattersList.innerHTML = chatters.map(chatter => `
            <div class="chatter-item d-flex align-items-center">
                <div class="chatter-avatar">
                    ${chatter.username.charAt(0).toUpperCase()}
                </div>
                <div class="chatter-info">
                    <div class="chatter-username">${this.escapeHtml(chatter.username)}</div>
                    <div class="chatter-stats">
                        ${chatter.messageCount} zpráv • Naposledy: ${this.formatTime(chatter.lastSeen)}
                    </div>
                    <div class="chatter-badges">
                        ${chatter.isModerator ? '<span class="chatter-badge badge-moderator">MOD</span>' : ''}
                        ${chatter.isSubscriber ? '<span class="chatter-badge badge-subscriber">SUB</span>' : ''}
                        ${chatter.isVip ? '<span class="chatter-badge badge-vip">VIP</span>' : ''}
                    </div>
                </div>
                <div class="chatter-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="dashboard.timeoutUser('${chatter.username}')">
                        <i class="fas fa-clock"></i>
                    </button>
                </div>
            </div>
        `).join('');
    }

    formatTime(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diff = now - time;

        if (diff < 60000) return 'právě teď';
        if (diff < 3600000) return `${Math.floor(diff / 60000)} min`;
        if (diff < 86400000) return `${Math.floor(diff / 3600000)} hod`;
        return `${Math.floor(diff / 86400000)} dní`;
    }

    async timeoutUser(username) {
        // Placeholder pro timeout funkci
        this.showToast(`Timeout pro ${username} (funkce bude přidána později)`, 'info');
    }
}

// Globální instance dashboardu
let dashboard;

// Inicializace dashboardu po načtení stránky
document.addEventListener('DOMContentLoaded', () => {
    dashboard = new Dashboard();
});
