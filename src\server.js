const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const Logger = require('./utils/logger');
require('dotenv').config();

const authRoutes = require('./routes/auth');
const dashboardRoutes = require('./routes/dashboard');
const botRoutes = require('./routes/bot');
const giveawayRoutes = require('./routes/giveaway');
const KickBot = require('./bot/KickBot');

const app = express();
const PORT = process.env.PORT || 8080;

// Globální bot instance
let globalBot = null;

// Middleware
app.use(cors());
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));
app.use(express.static(path.join(__dirname, '../public')));

// Inicializace bot instance
let botInstance = null;

// Sdílení KickAPI a Bot instance mezi routami
app.use((req, res, next) => {
    req.app.locals.kickAPI = authRoutes.kickAPI;

    // Vytvoření bot instance pokud neexistuje a máme platný token
    if (!botInstance && authRoutes.kickAPI && authRoutes.kickAPI.isTokenValid()) {
        botInstance = new KickBot(authRoutes.kickAPI);

        // Event listenery pro bot
        botInstance.on('connected', (data) => {
            Logger.info('Bot připojen k chatu', data);
        });

        botInstance.on('message', (data) => {
            Logger.debug('Chat zpráva', data);
        });

        botInstance.on('error', (error) => {
            Logger.error('Bot chyba', error.message);
        });
    }

    req.app.locals.botInstance = botInstance;
    next();
});

// Routes
app.use('/auth', authRoutes);
app.use('/dashboard', dashboardRoutes);
app.use('/dashboard/giveaway', giveawayRoutes);
app.use('/api/bot', botRoutes);

// OAuth callback route (musí být před hlavní stránkou)
app.get('/callback', (req, res) => {
    // Přesměrování na auth callback s query parametry
    const queryString = req.url.includes('?') ? req.url.substring(req.url.indexOf('?')) : '';
    res.redirect('/auth/callback' + queryString);
});

// Hlavní stránka
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../public/index.html'));
});

// Globální error handler
app.use((err, req, res, next) => {
    console.error('Error:', err);
    res.status(500).json({ error: 'Něco se pokazilo!' });
});

// Start serveru
app.listen(PORT, () => {
    console.log(`🚀 KickBot server běží na portu ${PORT}`);
    console.log(`📊 Dashboard: http://localhost:${PORT}`);
    console.log(`🔗 OAuth callback: http://localhost:${PORT}/callback`);
});

module.exports = app;
