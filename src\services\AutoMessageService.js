const Logger = require('../utils/Logger');

class AutoMessageService {
    constructor() {
        this.autoMessages = new Map();
        this.timers = new Map();
        this.isEnabled = false;
        this.bot = null;
    }

    // Nastavení bot instance
    setBot(bot) {
        this.bot = bot;
    }

    // Povolení/zakázání automatických zpráv
    setEnabled(enabled) {
        this.isEnabled = enabled;
        
        if (enabled) {
            this.startAllTimers();
            Logger.info('Automatické zprávy povoleny');
        } else {
            this.stopAllTimers();
            Logger.info('Automatické zprávy zakázány');
        }
    }

    // Přidání nové automatické zprávy
    addAutoMessage(messageData) {
        const { id, name, content, interval, enabled } = messageData;
        
        const autoMessage = {
            id: id || Date.now().toString(),
            name: name.trim(),
            content: content.trim(),
            interval: parseInt(interval) * 60000, // převod na milisekundy
            enabled: enabled === true || enabled === 'true',
            lastSent: null,
            createdAt: new Date()
        };

        this.autoMessages.set(autoMessage.id, autoMessage);

        if (autoMessage.enabled && this.isEnabled) {
            this.startTimer(autoMessage.id);
        }

        Logger.info('Automatická zpráva přidána', {
            id: autoMessage.id,
            name: autoMessage.name,
            interval: autoMessage.interval / 60000 + ' min'
        });

        return autoMessage;
    }

    // Aktualizace automatické zprávy
    updateAutoMessage(id, updateData) {
        const autoMessage = this.autoMessages.get(id);
        if (!autoMessage) {
            throw new Error('Automatická zpráva nenalezena');
        }

        // Zastavit starý timer
        this.stopTimer(id);

        // Aktualizovat data
        if (updateData.name !== undefined) autoMessage.name = updateData.name.trim();
        if (updateData.content !== undefined) autoMessage.content = updateData.content.trim();
        if (updateData.interval !== undefined) {
            autoMessage.interval = parseInt(updateData.interval) * 60000;
        }
        if (updateData.enabled !== undefined) {
            autoMessage.enabled = updateData.enabled === true || updateData.enabled === 'true';
        }

        autoMessage.updatedAt = new Date();

        // Spustit nový timer pokud je povoleno
        if (autoMessage.enabled && this.isEnabled) {
            this.startTimer(id);
        }

        Logger.info('Automatická zpráva aktualizována', { id, name: autoMessage.name });
        return autoMessage;
    }

    // Smazání automatické zprávy
    deleteAutoMessage(id) {
        const autoMessage = this.autoMessages.get(id);
        if (!autoMessage) {
            throw new Error('Automatická zpráva nenalezena');
        }

        this.stopTimer(id);
        this.autoMessages.delete(id);

        Logger.info('Automatická zpráva smazána', { id, name: autoMessage.name });
        return true;
    }

    // Získání všech automatických zpráv
    getAllAutoMessages() {
        return Array.from(this.autoMessages.values()).map(msg => ({
            ...msg,
            nextSend: this.getNextSendTime(msg.id)
        }));
    }

    // Spuštění timeru pro konkrétní zprávu
    startTimer(messageId) {
        const autoMessage = this.autoMessages.get(messageId);
        if (!autoMessage || !autoMessage.enabled) return;

        // Zastavit existující timer
        this.stopTimer(messageId);

        const timer = setInterval(async () => {
            await this.sendAutoMessage(messageId);
        }, autoMessage.interval);

        this.timers.set(messageId, timer);

        Logger.debug('Timer spuštěn pro automatickou zprávu', {
            id: messageId,
            interval: autoMessage.interval / 60000 + ' min'
        });
    }

    // Zastavení timeru pro konkrétní zprávu
    stopTimer(messageId) {
        const timer = this.timers.get(messageId);
        if (timer) {
            clearInterval(timer);
            this.timers.delete(messageId);
            Logger.debug('Timer zastaven pro automatickou zprávu', { id: messageId });
        }
    }

    // Spuštění všech timerů
    startAllTimers() {
        for (const [id, autoMessage] of this.autoMessages) {
            if (autoMessage.enabled) {
                this.startTimer(id);
            }
        }
    }

    // Zastavení všech timerů
    stopAllTimers() {
        for (const timerId of this.timers.keys()) {
            this.stopTimer(timerId);
        }
    }

    // Odeslání automatické zprávy
    async sendAutoMessage(messageId) {
        const autoMessage = this.autoMessages.get(messageId);
        if (!autoMessage || !autoMessage.enabled) return;

        if (!this.bot || !this.bot.isConnected) {
            Logger.debug('Bot není připojen, přeskakuji automatickou zprávu', { id: messageId });
            return;
        }

        try {
            // Nahrazení proměnných ve zprávě
            const processedMessage = this.processMessageVariables(autoMessage.content);
            
            await this.bot.sendMessage(processedMessage);
            
            autoMessage.lastSent = new Date();
            
            Logger.info('Automatická zpráva odeslána', {
                id: messageId,
                name: autoMessage.name,
                message: processedMessage
            });

        } catch (error) {
            Logger.error('Chyba při odesílání automatické zprávy', {
                id: messageId,
                error: error.message
            });
        }
    }

    // Zpracování proměnných ve zprávě
    processMessageVariables(content) {
        const now = new Date();
        const variables = {
            '{username}': this.bot?.kickAPI?.currentUser?.username || 'Bot',
            '{channel}': this.bot?.currentChannel || 'neznámý',
            '{time}': now.toLocaleTimeString('cs-CZ'),
            '{date}': now.toLocaleDateString('cs-CZ')
        };

        let processedContent = content;
        for (const [variable, value] of Object.entries(variables)) {
            processedContent = processedContent.replace(new RegExp(variable, 'g'), value);
        }

        return processedContent;
    }

    // Získání času dalšího odeslání
    getNextSendTime(messageId) {
        const autoMessage = this.autoMessages.get(messageId);
        if (!autoMessage || !autoMessage.enabled || !autoMessage.lastSent) {
            return null;
        }

        return new Date(autoMessage.lastSent.getTime() + autoMessage.interval);
    }

    // Manuální odeslání zprávy
    async sendManually(messageId) {
        const autoMessage = this.autoMessages.get(messageId);
        if (!autoMessage) {
            throw new Error('Automatická zpráva nenalezena');
        }

        await this.sendAutoMessage(messageId);
        return true;
    }

    // Získání statistik
    getStats() {
        const total = this.autoMessages.size;
        const enabled = Array.from(this.autoMessages.values()).filter(msg => msg.enabled).length;
        const activeTimers = this.timers.size;

        return {
            total,
            enabled,
            activeTimers,
            systemEnabled: this.isEnabled,
            botConnected: this.bot?.isConnected || false
        };
    }

    // Vyčištění všech dat
    clear() {
        this.stopAllTimers();
        this.autoMessages.clear();
        Logger.info('Všechny automatické zprávy vyčištěny');
    }
}

module.exports = AutoMessageService;
