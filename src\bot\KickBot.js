const EventEmitter = require('events');
const WebSocket = require('ws');
const config = require('../config/config');
const Logger = require('../utils/logger');
const AutoResponder = require('./AutoResponder');
const Statistics = require('./Statistics');

class KickBot extends EventEmitter {
    constructor(kickAPI) {
        super();
        this.kickAPI = kickAPI;
        this.ws = null;
        this.isConnected = false;
        this.currentChannel = null;
        this.channelId = null;
        this.chatRoomId = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 5000;
        
        // Bot statistiky
        this.stats = {
            startTime: null,
            messagesProcessed: 0,
            commandsExecuted: 0,
            errors: 0
        };
        
        // Příkazy bota
        this.commands = new Map();
        this.initializeDefaultCommands();

        // Cooldown pro příkazy
        this.commandCooldowns = new Map();

        // <PERSON><PERSON><PERSON>vědi
        this.autoResponder = new AutoResponder();

        // Statistiky
        this.statistics = new Statistics();
    }

    initializeDefaultCommands() {
        this.commands.set('!hello', {
            name: 'hello',
            response: 'Ahoj {user}! 👋',
            cooldown: 5,
            enabled: true
        });
        
        this.commands.set('!time', {
            name: 'time',
            response: 'Aktuální čas je: {time}',
            cooldown: 10,
            enabled: true
        });
        
        this.commands.set('!uptime', {
            name: 'uptime',
            response: 'Bot běží: {uptime}',
            cooldown: 15,
            enabled: true
        });
        
        this.commands.set('!commands', {
            name: 'commands',
            response: 'Dostupné příkazy: {commands}',
            cooldown: 30,
            enabled: true
        });
    }

    async connect(channelSlug) {
        try {
            if (!this.kickAPI || !this.kickAPI.isTokenValid()) {
                throw new Error('KickAPI není k dispozici nebo token není platný');
            }

            // Získání informací o kanálu
            const channelInfo = await this.kickAPI.getChannelInfo(channelSlug);
            this.currentChannel = channelInfo.slug;
            this.channelId = channelInfo.id;
            this.chatRoomId = channelInfo.chatroom?.id;

            if (!this.chatRoomId) {
                throw new Error('Chatroom ID nenalezeno - kanál možná neexistuje nebo nemá chat');
            }

            Logger.info('Informace o kanálu získány', {
                slug: channelInfo.slug,
                channel_id: channelInfo.id,
                chatroom_id: this.chatRoomId,
                is_live: channelInfo.livestream?.is_live || false
            });

            Logger.info('Připojování k chatu', {
                channel: this.currentChannel,
                channelId: this.channelId,
                chatRoomId: this.chatRoomId
            });

            // Pro nyní přeskočíme WebSocket a označíme jako připojeno
            // await this.connectWebSocket();

            this.isConnected = true;
            this.stats.startTime = new Date();
            this.statistics.startSession();
            this.emit('connected', { channel: this.currentChannel });

            Logger.success('Bot úspěšně připojen k API', {
                channel: this.currentChannel,
                channelId: this.channelId,
                note: 'WebSocket chat čtení bude přidáno později'
            });
            
        } catch (error) {
            Logger.error('Chyba při připojování bota', error.message);
            this.emit('error', error);
            throw error;
        }
    }

    async connectWebSocket() {
        return new Promise((resolve, reject) => {
            // Kick používá Pusher WebSocket s EU clusterem
            const wsUrl = 'wss://ws-eu.pusher.com/app/eb1d5f283081a78b932c?protocol=7&client=js&version=7.6.0&flash=false';

            this.ws = new WebSocket(wsUrl);

            this.ws.on('open', async () => {
                Logger.info('WebSocket připojen');

                // Čekání na connection response
                try {
                    const connectionData = await this.waitForMessage();
                    Logger.debug('Connection data received', { data: connectionData });

                    let connectionResponse;
                    if (typeof connectionData === 'string') {
                        connectionResponse = JSON.parse(connectionData);
                    } else {
                        connectionResponse = connectionData;
                    }

                    const socketId = connectionResponse.data ? JSON.parse(connectionResponse.data).socket_id : null;

                    Logger.info('Socket ID získáno', { socketId });

                    // Přihlášení k chat kanálu
                    await this.subscribeToChannel();

                    this.isConnected = true;
                    this.reconnectAttempts = 0;
                    resolve();

                } catch (error) {
                    Logger.error('Chyba při inicializaci WebSocket', error.message);
                    reject(error);
                }
            });

            this.ws.on('message', (data) => {
                this.handleWebSocketMessage(data);
            });

            this.ws.on('close', (code, reason) => {
                Logger.warn('WebSocket uzavřen', { code, reason: reason.toString() });
                this.isConnected = false;
                this.handleDisconnect();
            });

            this.ws.on('error', (error) => {
                Logger.error('WebSocket chyba', error.message);
                this.stats.errors++;
                reject(error);
            });

            // Timeout pro připojení
            setTimeout(() => {
                if (!this.isConnected) {
                    reject(new Error('WebSocket připojení vypršelo'));
                }
            }, 10000);
        });
    }

    waitForMessage() {
        return new Promise((resolve) => {
            const handler = (data) => {
                this.ws.removeListener('message', handler);
                resolve(data.toString());
            };
            this.ws.once('message', handler);
        });
    }

    async subscribeToChannel() {
        if (!this.ws || !this.chatRoomId) return;

        // Přihlášení k chat kanálu
        const subscribeMessage = {
            event: 'pusher:subscribe',
            data: {
                auth: '',
                channel: `chatrooms.${this.chatRoomId}.v2`
            }
        };

        this.ws.send(JSON.stringify(subscribeMessage));
        Logger.info('Přihlášen k chat kanálu', { chatRoomId: this.chatRoomId });

        // Čekání na subscription response
        try {
            const subscriptionResponse = await this.waitForMessage();
            Logger.info('Subscription response', { response: subscriptionResponse });
        } catch (error) {
            Logger.error('Chyba při subscription', error.message);
        }
    }

    handleWebSocketMessage(data) {
        try {
            let message;
            if (typeof data === 'string') {
                message = JSON.parse(data);
            } else if (data.toString) {
                message = JSON.parse(data.toString());
            } else {
                message = data;
            }

            // Ping/Pong pro udržení spojení
            if (message.event === 'pusher:ping') {
                this.ws.send(JSON.stringify({ event: 'pusher:pong', data: {} }));
                return;
            }

            // Chat zprávy - kontrola správného event názvu
            if (message.event && message.event.includes('ChatMessageEvent')) {
                this.handleChatMessage(message.data);
            }

            // Debug log pro neznámé eventy
            if (message.event && !message.event.startsWith('pusher:')) {
                Logger.debug('Neznámý event', { event: message.event, data: message.data });
            }

            this.stats.messagesProcessed++;

        } catch (error) {
            Logger.error('Chyba při zpracování WebSocket zprávy', {
                error: error.message,
                data: typeof data === 'string' ? data.substring(0, 100) : '[object]'
            });
            this.stats.errors++;
        }
    }

    handleChatMessage(messageData) {
        try {
            let chatMessage;
            if (typeof messageData === 'string') {
                chatMessage = JSON.parse(messageData);
            } else {
                chatMessage = messageData;
            }

            const { id, content, sender, created_at } = chatMessage;
            
            Logger.debug('Chat zpráva přijata', {
                user: sender.username,
                message: content,
                id
            });
            
            // Zaznamenání zprávy do statistik
            const isCommand = content.startsWith(config.bot.commandPrefix);
            this.statistics.recordMessage(sender.username, content, isCommand);

            this.emit('message', {
                id,
                content,
                username: sender.username,
                userId: sender.id,
                timestamp: created_at
            });

            // Zpracování příkazů
            if (isCommand) {
                this.handleCommand(content, sender);
            } else {
                // Zpracování automatických odpovědí
                this.handleAutoResponse(content, sender.username);
            }
            
        } catch (error) {
            Logger.error('Chyba při zpracování chat zprávy', error.message);
            this.stats.errors++;
        }
    }

    async handleCommand(message, sender) {
        const commandText = message.toLowerCase().trim();
        const command = this.commands.get(commandText.split(' ')[0]);
        
        if (!command || !command.enabled) {
            return;
        }
        
        // Kontrola cooldown
        const cooldownKey = `${sender.id}-${command.name}`;
        const now = Date.now();
        const lastUsed = this.commandCooldowns.get(cooldownKey);
        
        if (lastUsed && (now - lastUsed) < (command.cooldown * 1000)) {
            return; // Příkaz je v cooldown
        }
        
        try {
            const startTime = Date.now();

            // Nastavení cooldown
            this.commandCooldowns.set(cooldownKey, now);

            // Zpracování odpovědi
            let response = command.response;
            response = this.processResponseVariables(response, sender);

            // Poslání odpovědi
            await this.sendMessage(response);

            // Zaznamenání do statistik
            const responseTime = Date.now() - startTime;
            this.statistics.recordCommand(command.name, sender.username, responseTime);

            this.stats.commandsExecuted++;
            Logger.info('Příkaz vykonán', {
                command: command.name,
                user: sender.username,
                response,
                responseTime
            });
            
        } catch (error) {
            Logger.error('Chyba při vykonávání příkazu', error.message);
            this.stats.errors++;
            this.statistics.recordError('command_execution');
        }
    }

    processResponseVariables(response, sender) {
        const variables = {
            '{user}': sender.username,
            '{time}': new Date().toLocaleTimeString('cs-CZ'),
            '{uptime}': this.getUptime(),
            '{commands}': Array.from(this.commands.keys()).join(', ')
        };
        
        let processedResponse = response;
        for (const [variable, value] of Object.entries(variables)) {
            processedResponse = processedResponse.replace(new RegExp(variable, 'g'), value);
        }
        
        return processedResponse;
    }

    async sendMessage(message) {
        if (!this.kickAPI || !this.channelId) {
            throw new Error('Bot není připojen');
        }
        
        try {
            await this.kickAPI.sendChatMessage(this.channelId, message, 'bot');
            this.emit('messageSent', { message, channel: this.currentChannel });
        } catch (error) {
            Logger.error('Chyba při odesílání zprávy', error.message);
            throw error;
        }
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }
        
        this.isConnected = false;
        this.currentChannel = null;
        this.channelId = null;
        this.chatRoomId = null;
        
        Logger.info('Bot odpojen');
        this.emit('disconnected');
    }

    handleDisconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            Logger.info(`Pokus o znovupřipojení ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
            
            setTimeout(() => {
                if (this.currentChannel) {
                    this.connect(this.currentChannel).catch(error => {
                        Logger.error('Chyba při znovupřipojení', error.message);
                    });
                }
            }, this.reconnectDelay);
        } else {
            Logger.error('Maximální počet pokusů o znovupřipojení dosažen');
            this.emit('reconnectFailed');
        }
    }

    getUptime() {
        if (!this.stats.startTime) return '00:00:00';
        
        const uptime = Date.now() - this.stats.startTime.getTime();
        const hours = Math.floor(uptime / (1000 * 60 * 60));
        const minutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((uptime % (1000 * 60)) / 1000);
        
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    getStats() {
        return {
            ...this.stats,
            uptime: this.getUptime(),
            isConnected: this.isConnected,
            currentChannel: this.currentChannel,
            commandCount: this.commands.size
        };
    }

    addCommand(trigger, response, cooldown = 5) {
        this.commands.set(trigger.toLowerCase(), {
            name: trigger.replace('!', ''),
            response,
            cooldown,
            enabled: true
        });
    }

    removeCommand(trigger) {
        return this.commands.delete(trigger.toLowerCase());
    }

    async handleAutoResponse(message, username) {
        try {
            const responses = this.autoResponder.processMessage(message, username);

            for (const responseData of responses) {
                await this.sendMessage(responseData.response);

                // Zaznamenání do statistik
                this.statistics.recordAutoResponse(responseData.rule, username);

                this.emit('autoResponse', {
                    rule: responseData.rule,
                    response: responseData.response,
                    username,
                    originalMessage: message
                });
            }
        } catch (error) {
            Logger.error('Chyba při zpracování automatické odpovědi', error.message);
            this.stats.errors++;
            this.statistics.recordError('auto_response');
        }
    }

    getCommands() {
        return Array.from(this.commands.entries()).map(([trigger, command]) => ({
            trigger,
            ...command
        }));
    }

    getAutoResponder() {
        return this.autoResponder;
    }

    getStatistics() {
        return this.statistics;
    }

    disconnect() {
        if (this.ws) {
            this.ws.close();
            this.ws = null;
        }

        this.isConnected = false;
        this.currentChannel = null;
        this.statistics.endSession();

        Logger.info('Bot odpojen');
        this.emit('disconnected');
    }
}

module.exports = KickBot;
